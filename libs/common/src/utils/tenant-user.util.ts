import { Logger } from '@nestjs/common';

export const extractTenantUserFromRequest = async (
  request: any,
  configService: any,
): Promise<any> => {
  const logger = new Logger('extractTenantUserFromRequest');

  try {
    // Extract the frontend URL from origin or referer headers
    let websiteUrl = '';

    if (request.headers.origin) {
      websiteUrl = request.headers.origin;
      logger.log(`Using origin header: ${websiteUrl}`);
    } else if (request.headers.referer) {
      try {
        const refererUrl = new URL(request.headers.referer);
        websiteUrl = `${refererUrl.protocol}//${refererUrl.host}`;
        logger.log(`Using referer header: ${websiteUrl}`);
      } catch (error) {
        logger.error('Error parsing referer URL:', error);
      }
    }

    if (!websiteUrl) {
      logger.warn('No frontend URL could be determined from headers');
      return null;
    }

    // Find the configuration by website URL
    const config = await configService.findByWebsiteUrl(websiteUrl);

    if (!config) {
      logger.warn(`No configuration found for website URL: ${websiteUrl}`);
      return null;
    }

    if (!config.user) {
      logger.warn(`Configuration found but no user associated: ${websiteUrl}`);
      return null;
    }

    logger.log(`Found tenant user: ${config.user.id || config.user}`);
    return config.user;
  } catch (error) {
    logger.error('Error extracting tenant user:', error);
    return null;
  }
};
