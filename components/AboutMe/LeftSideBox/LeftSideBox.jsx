/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/AboutMe/LeftSideBox/LeftSideBox.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, July 20th 2023, 2:14:27 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

import {about} from '@/app/aboutme/data';
import {staticEducationData} from '@/app/who_am_i/educations/page';
import CodeContainer from '@/components/AboutMeSideMenu/CommentedText';
import MiniHeader from '@/components/AboutMeSideMenu/MiniHeader';
import Bio from '@/components/Bio/Bio';
import SkillSection from '@/components/SkillSection/SkillSection';
import EducationCard from '@/components/shared/EducationCard/EducationCard';
import ExperienceCard from '@/components/shared/ExperienceCard/ExperienceCard';
import {useEffect, useState} from 'react';
import Interest from '@/components/Interest/Interest';

export const API_BASE = process.env.NEXT_PUBLIC_ADMIN_API;

const staticData = [
  {
    logo: "data:image/png;base64,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",
    designation: "Software Engineer III",
    name: "jatri Service Ltd",
    companyDescription:
      "At Jatri, I work as a Software Engineer III, specializing in backend development with Nest.js. My role involves designing, developing, and maintaining scalable software solutions for the company's transportation platform. I collaborate with cross-functional teams to ensure efficient system architecture, optimize performance, and integrate advanced technologies to enhance user experiences. My work contributes to streamlining public transportation services and improving accessibility for users",
    startedAt: new Date("2024-11-01"),
    endedAt: null,
    companyWebsite: "https://jatri.co",
  },
  {
    logo: "data:image/png;base64,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",
    designation: "Backend Developer",
    name: "Quantigo Ai",
    companyDescription:
      "As a backend developer at Quantigo AI, I specialized in using Node.js, Express.js, Next.js, and MongoDB to develop efficient and scalable applications for high-quality data labeling services for machine learning models. I ensured that our annotation platform was reliable, robust, and delivered an exceptional user experience through my deep understanding of software engineering principles and attention to detail.",
    startedAt: new Date("2022-07-01"),
    endedAt: new Date("2024-11-10"),
    companyWebsite: "https://quantigo.ai",
  },
  {
    logo: "data:image/jpeg;base64,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",
    designation: "Full Stack Developer",
    name: "Core Devs Ltd.",
    companyDescription:
      "I worked as a full stack developer at Coredevs Ltd, where I gained expertise in React.js, Node.js, Express.js, and MongoDB. Our team delivered innovative software solutions to businesses using cutting-edge technologies. During my tenure, I worked on exciting projects and collaborated with some of the best developers in the industry.",
    startedAt: new Date("2021-12-01"),
    endedAt: new Date("2022-06-01"),
    companyWebsite: "https://coredevsltd.com/",
  },
];

const ExperienceSection = ({ data }) => (
  <div className="grid lg:grid-cols-2 gap-4 my-2 lg:m-4 ">
    {data.map((item) => (
      <ExperienceCard key={item.id || item.startedAt} item={item} />
    ))}
  </div>
);

const EducationSection = ({ data }) => (
  <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-4 my-2 lg:m-2 ">
    {data.map((item) => (
      <EducationCard key={item.id} item={item} />
    ))}
  </div>
);
const LeftSideBox = ({ currentSection, folder }) => {
  const [experienceData, setExperienceData] = useState([]);
  const [educationData, setEducationData] = useState([]);
  const [bioData, setBioData] = useState([]);
  const [interestData, setInterestData] = useState([]);
  const [skillsData, setSkillsData] = useState([]);

  useEffect(() => {
    if (folder === "experience") {
      fetch(`/api/company`)
        .then((res) => res.json())
        .then((data) => {
          data?.statusCode === 404 ? setExperienceData(staticData) : setExperienceData(data);
        })
        .catch(() => setExperienceData([]));
    }
    if (folder === "education") {
      fetch(`/api/education`)
        .then((res) => res.json())
        .then((data) => {
          data?.statusCode === 404 ? setEducationData(staticEducationData) : setEducationData(data);
        })
        .catch(() => setEducationData([]));
    }
    if (folder === "skills") {
      fetch(`/api/skill`)
        .then((res) => res.json())
        .then((data) => {
          data?.statusCode === 404 ? setSkillsData([]) : setSkillsData(data);
        })
        .catch(() => setSkillsData([]));
    }
    if (folder === "bio") {
      fetch(`/api/bio`)
        .then((res) => res.json())
        .then((data) => {
          data?.statusCode === 404 ? setBioData([]) : setBioData(data);
        })
        .catch(() => setBioData([]));
    }
    if (folder === "interests") {
      fetch(`/api/interest`)
        .then((res) => res.json())
        .then((data) => {
          data?.statusCode === 404 ? setInterestData([]) : setInterestData(data);
        })
        .catch(() => setInterestData([]));
    }
  }, [folder]);

  const renderItems = (folder) => {
    switch (folder) {
      case "experience":
        return <ExperienceSection data={experienceData} />;
      case "education":
        return <EducationSection data={educationData} />;
      case "skills":
        return <SkillSection data={skillsData} />;
      case "bio":
        return <Bio data={bioData} />;
      case "interests":
        return <Interest data={interestData} />;
      default:
        return <CodeContainer text={about.sections[currentSection].info[folder].description}></CodeContainer>;
    }
  };
  return (
    <>
      <div id="left" className="w-full flex flex-col border-right">
        <MiniHeader currentSection={currentSection} folder={folder} />

        <div id="tab-mobile" className="flex lg:hidden font-fira_retina">
          <span className="text-white">{"//"} </span>
          <h3
            className="text-white px-2"
            dangerouslySetInnerHTML={{
              __html: about.sections[currentSection]?.title,
            }}
          ></h3>
          <span className="text-menu-text"> / </span>
          <h3
            className="text-menu-text pl-2"
            dangerouslySetInnerHTML={{
              __html: about.sections[currentSection]?.info[folder]?.title,
            }}
          ></h3>
        </div>

        <div id="commented-text" className="flex h-full w-full lg:border-right overflow-hidden">
          <div className="w-full h-full ml-2 lg:ml-5 lg:mr-10 mr-2 lg:my-5 overflow-scroll">{renderItems(folder)}</div>

          <div id="scroll-bar" className="h-full border-left hidden lg:flex justify-center py-1">
            <div id="scroll"></div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LeftSideBox;
