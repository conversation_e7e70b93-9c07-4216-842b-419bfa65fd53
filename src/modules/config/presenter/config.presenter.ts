import { ConfigEntity, UserEntity } from '@app/repository';

class UserPresenter {
  id?: string;
  name: string;
  email: string;
  dob?: Date;
  phone?: string;
  status?: boolean = true;

  constructor(user: UserEntity) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.dob = user.dob;
    this.phone = user.phone;
    this.status = user.status;
  }
}

export class ConfigPresenter {
  id?: string;
  emailjsServiceId: string;
  emailjsTemplateId: string;
  emailjsPublicKey: string;
  emailjsPrivateKey: string;
  theme: string;
  language: string;
  websiteUrl?: string;
  active: boolean = true;
  isDefault: boolean = false;
  isEmailReceivedOn: boolean = true;
  isSmsReceivedOn: boolean = false;
  isPushNotificationReceivedOn: boolean = false;
  isVisitorLogOn: boolean = false;
  isResumeVisibilityOn: boolean = true;
  githubURL?: string;
  leetcodeURL?: string;
  linkedInURL?: string;
  image?: string;
  user?: UserPresenter;
  activeTill?: Date;
  createdAt?: Date;
  updatedAt?: Date;

  constructor(config: ConfigEntity) {
    this.id = config.id;
    this.emailjsServiceId = config.emailjsServiceId;
    this.emailjsTemplateId = config.emailjsTemplateId;
    this.emailjsPublicKey = config.emailjsPublicKey;
    this.emailjsPrivateKey = config.emailjsPrivateKey;
    this.theme = config.theme;
    this.language = config.language;
    this.websiteUrl = config.websiteUrl;
    this.active = config.active;
    this.isDefault = config.isDefault;
    this.isEmailReceivedOn = config.isEmailReceivedOn;
    this.isSmsReceivedOn = config.isSmsReceivedOn;
    this.isPushNotificationReceivedOn = config.isPushNotificationReceivedOn;
    this.isVisitorLogOn = config.isVisitorLogOn;
    this.isResumeVisibilityOn = config.isResumeVisibilityOn;
    this.githubURL = config.githubURL;
    this.leetcodeURL = config.leetcodeURL;
    this.linkedInURL = config.linkedInURL;
    this.image = config.image;
    this.user = new UserPresenter(config.user);
    this.activeTill = config.activeTill;
    this.createdAt = config.createdAt;
    this.updatedAt = config.updatedAt;
  }
}
