import { IsBoolean, Is<PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateObjectiveDto {
  @IsNotEmpty()
  @IsString()
  tag: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active: boolean;
}
