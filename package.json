{"name": "portfolio-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^11.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@types/bcrypt": "^5.0.2", "argon2": "^0.43.0", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cache-manager-redis-yet": "^5.1.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.6.0", "compression": "^1.8.0", "helmet": "^8.1.0", "mongodb": "^6.16.0", "mongoose": "^8.14.0", "mongoose-paginate-v2": "^1.9.0", "mongoose-unique-validator": "^4.0.1", "passport": "^0.7.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/db(|/.*)$": "<rootDir>/libs/db/src/$1", "^@app/repository(|/.*)$": "<rootDir>/libs/repository/src/$1", "^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1", "^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1"}}}