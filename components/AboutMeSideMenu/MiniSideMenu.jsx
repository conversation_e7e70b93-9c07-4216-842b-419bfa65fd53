/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/AboutMeSideMenu/MiniSideMenu.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Saturday, July 15th 2023, 1:14:03 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */
import {about} from '@/app/aboutme/data';
import Image from 'next/image';
import React from 'react';

const MiniSideMenu = ({ currentSection, focusCurrentSection }) => {
  return (
    <>
      <div id="sections" className=" hidden lg:block">
        {Object.keys(about.sections).map((section, index) => (
          <div
            id="section-icon"
            key={section}
            className={currentSection === section ? "active" : ""}
          >
            <Image
              id={"section-icon-" + section}
              src={about.sections[section].icon}
              alt={about.sections[section].title + "-section"}
              onClick={() => focusCurrentSection(section)}
            ></Image>
          </div>
        ))}
      </div>
    </>
  );
};

export default MiniSideMenu;
