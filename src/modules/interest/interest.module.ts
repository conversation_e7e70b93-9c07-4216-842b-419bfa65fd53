import { Module } from '@nestjs/common';
import { InterestService } from './interest.service';
import { InterestController } from './interest.controller';
import { JwtService } from '@nestjs/jwt';
import { RepositoryModule } from '@app/repository';
import { UserConfigModule } from '../config/config.module';
import { CloudinaryModule } from '@app/common/config/cloudinary';

@Module({
  imports: [RepositoryModule, CloudinaryModule, UserConfigModule],
  controllers: [InterestController],
  providers: [InterestService, JwtService],
})
export class InterestModule {}
