import { Injectable, Logger } from '@nestjs/common';
import { IDataServices, UserEntity } from '@app/repository';
import {
  CreateConfigDto,
  GetConfigPaginationDto,
  UpdateConfigDto,
} from './dto';
import {
  create,
  deleteById,
  getAll,
  getAllForTenant,
  getById,
  updateById,
} from './services';
import { CloudinaryService } from '@app/common/config/cloudinary';
import { uploadFileToCloudinary } from '@app/common/utils';
import { CloudinaryFolder } from '@app/common';

@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private cloudinaryAvailable = true;
  constructor(
    private readonly db: IDataServices,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  async create(
    user: UserEntity,
    data: CreateConfigDto,
    image?: Express.Multer.File,
  ) {
    let imageUrl: string | undefined;
    if (image) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          image.buffer,
          this.cloudinaryService,
          CloudinaryFolder.CONFIG,
        );
        imageUrl = uploadResult.url;
      } catch (error) {
        this.logger.error('Error uploading image to Cloudinary:', error);
        // Continue without the image URL if upload fails
      }
    }

    return create(user, data, this.db, imageUrl);
  }

  findAll(user: UserEntity, paginationDto?: GetConfigPaginationDto) {
    return getAll(this.db, user, paginationDto);
  }

  findOne(user: UserEntity, id: string) {
    return getById(user, id, this.db);
  }

  async update(
    user: UserEntity,
    id: string,
    data: UpdateConfigDto,
    image?: Express.Multer.File,
  ) {
    let imageUrl: string | undefined;

    if (image && this.cloudinaryAvailable) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          image.buffer,
          this.cloudinaryService,
          CloudinaryFolder.CONFIG,
        );
        imageUrl = uploadResult.url;
      } catch (error) {
        this.logger.error(
          'Error uploading image to Cloudinary for update:',
          error,
        );
        // Continue without the image URL if upload fails
      }
    } else if (image && !this.cloudinaryAvailable) {
      this.logger.warn(
        'Cloudinary is not available. Using placeholder image URL for update.',
      );
      // Provide a placeholder URL when Cloudinary is not available
      imageUrl = 'https://via.placeholder.com/150?text=Config+Image';
    }

    return updateById(user, id, data, this.db, imageUrl);
  }

  remove(user: UserEntity, id: string) {
    return deleteById(user, id, this.db);
  }

  async findByWebsiteUrl(websiteUrl: string) {
    return await this.db.configs.findByWebsiteUrl(websiteUrl);
  }

  async findAllByWebsiteUrl(request: any) {
    return getAllForTenant(request, this.db, this);
  }

  async checkWebsiteUrlUniqueness(websiteUrl: string, excludeId?: string) {
    const existingConfig = await this.db.configs.findByWebsiteUrl(websiteUrl);
    if (existingConfig && (!excludeId || existingConfig.id !== excludeId)) {
      throw new Error(
        `Website URL "${websiteUrl}" is already in use by another configuration`,
      );
    }
    return true;
  }
}
