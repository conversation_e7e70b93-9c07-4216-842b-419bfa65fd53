import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ConfigService } from './config.service';
import { CurrentUser, JwtAuthGuard } from '@app/common';
import { UserEntity } from '@app/repository';
import {
  CreateConfigDto,
  GetConfigPaginationDto,
  UpdateConfigDto,
} from './dto';
import { Request } from 'express';

@Controller({
  path: 'config',
  version: '1',
})
export class ConfigController {
  constructor(private readonly configService: ConfigService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @UseInterceptors(FileInterceptor('image'))
  async create(
    @CurrentUser() user: UserEntity,
    @Body() data: CreateConfigDto,
    @UploadedFile() image?: Express.Multer.File,
  ) {
    // Check website URL uniqueness if provided
    if (data.websiteUrl) {
      await this.configService.checkWebsiteUrlUniqueness(data.websiteUrl);
    }
    return this.configService.create(user, data, image);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(
    @CurrentUser() user: UserEntity,
    @Query() paginationDto: GetConfigPaginationDto,
  ) {
    return this.configService.findAll(user, paginationDto);
  }

  @Get('data')
  async findAllForPortfolio(@Req() request: Request) {
    return this.configService.findAllByWebsiteUrl(request);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  findOne(@CurrentUser() user: UserEntity, @Param('id') id: string) {
    return this.configService.findOne(user, id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  @UseInterceptors(FileInterceptor('image'))
  async update(
    @CurrentUser() user: UserEntity,
    @Param('id') id: string,
    @Body() updateConfigDto: UpdateConfigDto,
    @UploadedFile() image?: Express.Multer.File,
  ) {
    // Check website URL uniqueness if provided and different from current
    if (updateConfigDto.websiteUrl) {
      await this.configService.checkWebsiteUrlUniqueness(
        updateConfigDto.websiteUrl,
        id,
      );
    }
    return this.configService.update(user, id, updateConfigDto, image);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  remove(@CurrentUser() user: UserEntity, @Param('id') id: string) {
    return this.configService.remove(user, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('website/:websiteUrl')
  findByWebsiteUrl(@Param('websiteUrl') websiteUrl: string) {
    return this.configService.findByWebsiteUrl(websiteUrl);
  }
}
