import { Injectable, Logger } from '@nestjs/common';
import { IDataServices } from '@app/repository';
import { CreateEmailDto } from './dto';
import { create } from './services';
import { ConfigService as CFG } from '@nestjs/config';
import { ConfigService } from '../config/config.service';

// import { ConfigService } from '../config/config.service';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly db: IDataServices,
    private readonly configService: ConfigService,
    private readonly cfgService: CFG,
  ) {}

  async create(request: any, data: CreateEmailDto) {
    return create(request, data, this.db, this.configService, this.cfgService);
  }
}
