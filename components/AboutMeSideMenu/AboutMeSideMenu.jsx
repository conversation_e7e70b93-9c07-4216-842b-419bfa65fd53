/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/AboutMeSideMenu/AboutMeSideMenu.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Saturday, July 15th 2023, 1:09:11 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */
import {about, contact} from '@/app/aboutme/data';
import Image from 'next/image';
import MiniSideMenu from './MiniSideMenu';
import arrow from '/Assets/icons/arrow.svg';
import diple from '/Assets/icons/diple.svg';
import email from '/Assets/icons/email.svg';
import folder1 from '/Assets/icons/folder1.svg';
import folder2 from '/Assets/icons/folder2.svg';
import folder3 from '/Assets/icons/folder3.svg';
import phone from '/Assets/icons/phone.svg';

const folders = [folder1, folder2, folder3];
const contactIcons = [email, phone];

const AboutMeSideMenu = ({
  currentSection,
  focusCurrentSection,
  focusCurrentFolder,
  currentFolder,
  showContacts,
}) => {
  return (
    <>
      <div id="page-menu" className=" w-full lg:flex md:hidden ">
        <MiniSideMenu
          currentSection={currentSection}
          focusCurrentSection={focusCurrentSection}
        />

        <div
          id="section-content"
          className="hidden  lg:block w-full h-full border-right"
        >
          <div
            id="section-content-title"
            className=" hidden lg:flex items-center min-w-full"
          >
            <Image
              id="section-arrow-menu"
              src={arrow}
              alt="tech"
              className="section-arrow mx-3 open"
            ></Image>
            <p
              className="font-fira_regular text-white text-sm"
              dangerouslySetInnerHTML={{
                __html: about.sections[currentSection].title,
              }}
            ></p>
          </div>
          <div>
            {Object.keys(about.sections[currentSection].info).map(
              (folder, key, index) => (
                <div
                  className="grid grid-cols-2 items-center my-2 font-fira_regular text-menu-text"
                  onClick={() => focusCurrentFolder(folder)}
                  key={key}
                >
                  <div
                    className={
                      currentFolder === folder
                        ? "active flex col-span-2 hover:text-white hover:cursor-pointer"
                        : "flex col-span-2 hover:text-white hover:cursor-pointer"
                    }
                  >
                    <Image
                      id="diple"
                      src={diple}
                      // TODO need to fix it
                      className={currentFolder === folder ? "open" : ""}
                      alt="tech"
                    ></Image>
                    <Image src={folders[key]} alt="" className="mr-3"></Image>
                    <p
                      id={folder}
                      dangerouslySetInnerHTML={{ __html: folder }}
                    ></p>
                  </div>
                  {folder.files !== undefined && (
                    <div className="col-span-2">
                      {folder.files.map((file, key) => (
                        <div
                          className="hover:text-white hover:cursor-pointer flex my-2"
                          key={key}
                        >
                          <Image
                            src={markdown}
                            alt="tech"
                            className="ml-8 mr-3"
                          ></Image>
                          <p>{folder}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )
            )}
          </div>

          <div
            id="section-content-title-contact"
            className=" flex  items-center min-w-full border-top"
          >
            <Image
              id="section-arrow-menu"
              src={arrow}
              alt="tech"
              className="section-arrow mx-3 open"
            ></Image>
            <p
              className="font-fira_regular text-white text-sm"
              dangerouslySetInnerHTML={{
                __html: contact.direct.title,
              }}
            ></p>
          </div>
          <div id="contact-sources" className="hidden lg:flex lg:flex-col my-2">
            {contact.direct.sources.map((source, key) => (
              <div className="flex items-center mb-2" key={key}>
                <Image
                  src={contactIcons[key]}
                  width={12}
                  alt="tech"
                  className="mx-2"
                ></Image>
                <a
                  href="/"
                  className="font-fira_retina text-menu-text hover:text-white"
                  dangerouslySetInnerHTML={{ __html: source }}
                ></a>
              </div>
            ))}
          </div>
        </div>

        <div
          id="section-content"
          className="lg:hidden md:hidden  w-full font-fira_regular"
        >
          {Object.keys(about.sections).map((section) => (
            <div key={section}>
              <div
                src={section.icon}
                id="section-content-title"
                className=" flex lg:hidden mb-1"
                onClick={() => focusCurrentSection(section)}
              >
                <Image
                  src={arrow}
                  id={"section-arrow-" + section}
                  alt="tech"
                  className="section-arrow"
                ></Image>
                <p
                  className="text-white text-sm"
                  dangerouslySetInnerHTML={{ __html: section }}
                ></p>
              </div>

              <div id={"folders-" + section} className="hidden">
                {Object.keys(about.sections[section].info).map(
                  (folder, key, index) => (
                    <div
                      className="grid grid-cols-2 items-center my-2 font-fira_regular text-menu-text hover:text-white hover:cursor-pointer"
                      onClick={() => focusCurrentFolder(folder)}
                      key={key}
                    >
                      <div className="flex col-span-2">
                        <Image id="diple" alt="diple" src={diple}></Image>
                        <Image
                          src={folders[key]}
                          alt="tech"
                          className="mr-3"
                        ></Image>
                        <p
                          id={folder}
                          dangerouslySetInnerHTML={{ __html: folder }}
                        ></p>
                      </div>
                      {folder.files !== undefined && (
                        <div className="col-span-2">
                          {folder.files.map((file, key) => (
                            <div
                              className="hover:text-white hover:cursor-pointer flex my-2"
                              key={key}
                            >
                              <Image
                                src={markdown}
                                alt="tech"
                                className="ml-8 mr-3"
                              ></Image>
                              <p>{folder}</p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )
                )}
              </div>
            </div>
          ))}

          <div
            id="section-content-title"
            className=" flex items-center min-w-full"
            onClick={showContacts}
          >
            <Image
              src={arrow}
              alt="tech"
              id="section-arrow"
              className="section-arrow"
            ></Image>
            <p
              className="font-fira_regular text-white text-sm"
              dangerouslySetInnerHTML={{
                __html: contact.direct.title,
              }}
            ></p>
          </div>
          <div id="contacts" className="hidden">
            {Object.keys(contact.direct.sources).map((source, key) => (
              <div className="flex items-center my-2" key={key}>
                <Image src={contactIcons[key]} alt="tech"></Image>
                <a
                  href="/"
                  className="font-fira_retina text-menu-text hover:text-white ml-4"
                  dangerouslySetInnerHTML={{
                    __html: contact.direct.sources[source],
                  }}
                ></a>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default AboutMeSideMenu;
