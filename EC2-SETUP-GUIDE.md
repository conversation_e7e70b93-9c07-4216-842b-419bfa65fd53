# EC2 Setup Guide for Admin Backend Deployment

## Your EC2 Instance Details
- **Instance ID**: `i-0b7684f90645bb84f`
- **Public IP**: `*************`
- **Region**: `ap-south-1` (Asia Pacific - Mumbai)
- **Availability Zone**: `ap-south-1c`
- **Instance Type**: `t3.large`

## Quick Setup Commands

### 1. Connect to Your EC2 Instance
```bash
# Replace 'your-key.pem' with your actual key file
ssh -i your-key.pem ubuntu@*************
```

### 2. Run the Setup Script
```bash
# Download and run the setup script
curl -fsSL https://raw.githubusercontent.com/your-username/portfolio-full/main/admin-backend/scripts/setup-ec2.sh -o setup-ec2.sh
chmod +x setup-ec2.sh
sudo ./setup-ec2.sh
```

### 3. Configure Environment Variables
```bash
# Navigate to app directory
cd /opt/admin-backend

# Copy production environment template
sudo cp .env.production .env

# Edit with your values (already configured for your instance)
sudo nano .env
```

## GitHub Secrets Configuration

Add these secrets to your GitHub repository:

| Secret Name | Value |
|-------------|-------|
| `EC2_HOST` | `*************` |
| `EC2_USERNAME` | `ubuntu` (or `deploy` if you create a deploy user) |
| `EC2_PRIVATE_KEY` | Your SSH private key content |

## SSH Key Setup

### Generate SSH Key for GitHub Actions:
```bash
# On your local machine
ssh-keygen -t rsa -b 4096 -C "github-actions@portfolio" -f ~/.ssh/portfolio-deploy

# Copy public key to EC2
ssh-copy-id -i ~/.ssh/portfolio-deploy.pub ubuntu@*************

# Get private key for GitHub secret
cat ~/.ssh/portfolio-deploy
```

## Security Group Configuration

Ensure your EC2 security group allows these ports:
- **Port 22**: SSH access
- **Port 80**: HTTP (Nginx)
- **Port 443**: HTTPS (if using SSL)
- **Port 8080**: Application (for direct access if needed)

## Application URLs

After deployment, your application will be available at:
- **Main Application**: `http://*************/api`
- **Health Check**: `http://*************/api/health`
- **Direct Backend**: `http://*************:8080/api`

## Monitoring Commands

```bash
# Check application status
cd /opt/admin-backend
sudo ./scripts/status.sh

# View application logs
sudo docker-compose -f docker-compose.prod.yml logs -f

# Check system resources
htop

# Check disk space
df -h

# Check memory usage
free -h
```

## Troubleshooting

### If deployment fails:
1. Check GitHub Actions logs
2. SSH to EC2 and check logs:
   ```bash
   cd /opt/admin-backend
   sudo docker-compose -f docker-compose.prod.yml logs
   ```

### If application won't start:
1. Check environment variables:
   ```bash
   cd /opt/admin-backend
   cat .env
   ```
2. Test database connection
3. Check available memory and disk space

### Common Issues:
- **Out of disk space**: Clean up with `docker system prune -f`
- **Memory issues**: Consider upgrading instance or adding swap
- **Permission issues**: Ensure proper ownership of `/opt/admin-backend`

## Manual Deployment

If you need to deploy manually:
```bash
cd /opt/admin-backend
git pull origin main
sudo ./scripts/deploy.sh
```

## Backup Strategy

### Database Backup (MongoDB Atlas):
Your database is already on MongoDB Atlas, which provides automatic backups.

### Application Backup:
```bash
# Backup application files
sudo tar -czf /tmp/admin-backend-backup-$(date +%Y%m%d).tar.gz /opt/admin-backend

# Backup environment file
sudo cp /opt/admin-backend/.env /tmp/env-backup-$(date +%Y%m%d)
```

## Performance Optimization

### For t3.large instance:
- **Memory**: 8 GB RAM - sufficient for the application
- **CPU**: 2 vCPUs - good for moderate traffic
- **Storage**: Monitor disk usage regularly

### Recommended monitoring:
```bash
# Add to crontab for regular monitoring
crontab -e

# Add this line for daily disk cleanup
0 2 * * * docker system prune -f

# Add this line for weekly backup
0 3 * * 0 tar -czf /tmp/backup-$(date +\%Y\%m\%d).tar.gz /opt/admin-backend
```

## Next Steps

1. **Set up GitHub secrets** with the values above
2. **Test SSH connection** to your EC2 instance
3. **Run the setup script** on EC2
4. **Push to main branch** to trigger first deployment
5. **Monitor the deployment** through GitHub Actions
6. **Test the application** at `http://*************/api/health`

## Support

If you encounter issues:
1. Check the deployment logs in GitHub Actions
2. SSH to EC2 and run `./scripts/status.sh`
3. Review application logs with Docker Compose
4. Check system resources (memory, disk, CPU)
