import { CreateEmailDto } from '../dto';
import { IDataServices } from '@app/repository';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigService as CFG } from '@nestjs/config';
import { ConfigService } from '../../config/config.service';

export const create = async (
  request: any,
  emailData: CreateEmailDto,
  db: IDataServices,
  configService: ConfigService,
  cfg: CFG,
) => {
  const tenantUser = await extractTenantUserFromRequest(request, configService);
  const config = await db.configs.findOne({ user: tenantUser });

  const data = {
    emailjsServiceId: config.emailjsServiceId,
    emailjsTemplateId: config.emailjsTemplateId,
    emailjsPublicKey: config.emailjsPublicKey,
    emailjsPrivateKey: config.emailjsPrivateKey,
  };
  console.log('🚀 ~ create ~ data: ', data);

  return data;
};
