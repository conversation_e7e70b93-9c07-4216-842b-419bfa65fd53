import { IDataServices } from '@app/repository';
import { EducationPresenter } from '../presenter';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigService } from '../../config/config.service';
import { Logger } from '@nestjs/common';

export const getAllForTenant = async (
  request: any,
  db: IDataServices,
  configService: ConfigService,
) => {
  const logger = new Logger('getAllForTenant');

  try {
    // Extract tenant user from request
    request.headers.origin = 'http://localhost:5000';
    const tenantUser = await extractTenantUserFromRequest(
      request,
      configService,
    );

    if (!tenantUser) {
      logger.warn('No tenant user found for request');
      return [];
    }

    logger.log(
      `Fetching education for tenant user: ${tenantUser.id || tenantUser}`,
    );

    // Fetch education for this specific user
    const education = await db.educations.findAll(
      {
        user: tenantUser,
        active: true,
      },
      {
        options: {
          sort: {
            startedAt: -1,
          },
        },
      },
    );

    logger.log(`Found ${education.length} education records for tenant user`);

    // Apply presenter and return only active education records
    return education
      .filter((edu) => edu.active)
      .map((edu) => new EducationPresenter(edu));
  } catch (error) {
    logger.error('Error in getAllForTenant:', error);
    return [];
  }
};
